
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { Teacher, Classroom, Level, TimeSlot } from '@/lib/schemas';
import { scheduleApi } from '@/lib/api';
import { notify, withNotifications } from '@/lib/notifications';

interface StoreState {
  teachers: Teacher[];
  classrooms: Classroom[];
  levels: Level[];
  timeSlots: TimeSlot[];
  availableTimeSlots: string[];
  selectedTeacher: string | null;
  selectedClassroom: string | null;
  isLoading: boolean;

  // Actions
  setTeachers: (teachers: Teacher[]) => void;
  addTeacher: (teacher: Teacher) => void;
  updateTeacher: (id: string, teacher: Partial<Teacher>) => void;
  deleteTeacher: (id: string) => void;

  setClassrooms: (classrooms: Classroom[]) => void;
  addClassroom: (classroom: Classroom) => void;
  updateClassroom: (id: string, classroom: Partial<Classroom>) => void;
  deleteClassroom: (id: string) => void;

  setLevels: (levels: Level[]) => void;
  addLevel: (level: Level) => void;
  updateLevel: (id: string, level: Partial<Level>) => void;
  deleteLevel: (id: string) => void;

  setTimeSlots: (timeSlots: TimeSlot[]) => void;
  addTimeSlot: (timeSlot: TimeSlot) => void;
  updateTimeSlot: (id: string, timeSlot: Partial<TimeSlot>) => void;
  deleteTimeSlot: (id: string) => void;

  setAvailableTimeSlots: (timeSlots: string[]) => void;
  addAvailableTimeSlot: (timeSlot: string) => void;
  removeAvailableTimeSlot: (timeSlot: string) => void;

  setSelectedTeacher: (teacherId: string | null) => void;
  setSelectedClassroom: (classroomId: string | null) => void;

  // API Actions
  loadScheduleData: () => Promise<void>;
  saveScheduleData: () => Promise<void>;
  createTeacherAsync: (teacher: Omit<Teacher, 'id'>) => Promise<void>;
  updateTeacherAsync: (id: string, teacher: Partial<Teacher>) => Promise<void>;
  deleteTeacherAsync: (id: string) => Promise<void>;
  createClassroomAsync: (classroom: Omit<Classroom, 'id'>) => Promise<void>;
  updateClassroomAsync: (id: string, classroom: Partial<Classroom>) => Promise<void>;
  deleteClassroomAsync: (id: string) => Promise<void>;
  createLevelAsync: (level: Omit<Level, 'id'>) => Promise<void>;
  updateLevelAsync: (id: string, level: Partial<Level>) => Promise<void>;
  deleteLevelAsync: (id: string) => Promise<void>;
  createTimeSlotAsync: (timeSlot: Omit<TimeSlot, 'id'>) => Promise<void>;
  updateTimeSlotAsync: (id: string, timeSlot: Partial<TimeSlot>) => Promise<void>;
  deleteTimeSlotAsync: (id: string) => Promise<void>;

  // Utilities
  getFilteredTimeSlots: () => TimeSlot[];
  checkConflict: (newSlot: Omit<TimeSlot, 'id'>) => boolean;
  resetToDefaults: () => void;
  testApiConnection: () => Promise<void>;
}

export const useStore = create<StoreState>()(
  persist(
    (set, get) => ({
      teachers: [],
      classrooms: [],
      levels: [],
      timeSlots: [],
      availableTimeSlots: ['08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00'],
      selectedTeacher: null,
      selectedClassroom: null,
      isLoading: false,

  setTeachers: (teachers) => set({ teachers }),
  addTeacher: (teacher) => set((state) => ({ teachers: [...state.teachers, teacher] })),
  updateTeacher: (id, updates) =>
    set((state) => ({
      teachers: state.teachers.map((t) => (t.id === id ? { ...t, ...updates } : t)),
    })),
  deleteTeacher: (id) =>
    set((state) => ({
      teachers: state.teachers.filter((t) => t.id !== id),
      timeSlots: state.timeSlots.filter((ts) => ts.teacherId !== id),
    })),

  setClassrooms: (classrooms) => set({ classrooms }),
  addClassroom: (classroom) => set((state) => ({ classrooms: [...state.classrooms, classroom] })),
  updateClassroom: (id, updates) =>
    set((state) => ({
      classrooms: state.classrooms.map((c) => (c.id === id ? { ...c, ...updates } : c)),
    })),
  deleteClassroom: (id) =>
    set((state) => ({
      classrooms: state.classrooms.filter((c) => c.id !== id),
      timeSlots: state.timeSlots.filter((ts) => ts.classroomId !== id),
    })),

  setLevels: (levels) => set({ levels }),
  addLevel: (level) => set((state) => ({ levels: [...state.levels, level] })),
  updateLevel: (id, updates) =>
    set((state) => ({
      levels: state.levels.map((l) => (l.id === id ? { ...l, ...updates } : l)),
    })),
  deleteLevel: (id) =>
    set((state) => ({
      levels: state.levels.filter((l) => l.id !== id),
      timeSlots: state.timeSlots.filter((ts) => ts.levelId !== id),
    })),

  setTimeSlots: (timeSlots) => set({ timeSlots }),
  addTimeSlot: (timeSlot) => set((state) => ({ timeSlots: [...state.timeSlots, timeSlot] })),
  updateTimeSlot: (id, updates) =>
    set((state) => ({
      timeSlots: state.timeSlots.map((ts) => (ts.id === id ? { ...ts, ...updates } : ts)),
    })),
  deleteTimeSlot: (id) =>
    set((state) => ({
      timeSlots: state.timeSlots.filter((ts) => ts.id !== id),
    })),

  setAvailableTimeSlots: (timeSlots) => set({ availableTimeSlots: timeSlots }),
  addAvailableTimeSlot: (timeSlot) =>
    set((state) => ({
      availableTimeSlots: [...state.availableTimeSlots, timeSlot].sort()
    })),
  removeAvailableTimeSlot: (timeSlot) =>
    set((state) => ({
      availableTimeSlots: state.availableTimeSlots.filter((ts) => ts !== timeSlot),
    })),

  setSelectedTeacher: (teacherId) => set({ selectedTeacher: teacherId }),
  setSelectedClassroom: (classroomId) => set({ selectedClassroom: classroomId }),

  // API Actions
  loadScheduleData: async () => {
    try {
      set({ isLoading: true });
      console.log('🔄 Loading schedule data from API...');
      const data = await scheduleApi.getScheduleData();
      console.log('📊 Raw API response:', data);

      // Transform data if needed (ensure _id becomes id)
      const transformedData = {
        teachers: data.teachers?.map((teacher: any) => ({
          id: teacher.id || teacher._id,
          name: teacher.name,
          color: teacher.color,
        })) || [],
        classrooms: data.classrooms?.map((classroom: any) => ({
          id: classroom.id || classroom._id,
          name: classroom.name,
          capacity: classroom.capacity,
        })) || [],
        levels: data.levels?.map((level: any) => ({
          id: level.id || level._id,
          label: level.label,
          color: level.color,
        })) || [],
        timeSlots: data.timeSlots?.map((slot: any) => ({
          id: slot.id || slot._id,
          teacherId: slot.teacherId?.id || slot.teacherId?._id || slot.teacherId,
          classroomId: slot.classroomId?.id || slot.classroomId?._id || slot.classroomId,
          levelId: slot.levelId?.id || slot.levelId?._id || slot.levelId,
          start: slot.start,
          end: slot.end,
          dayOfWeek: slot.dayOfWeek,
        })) || [],
      };

      console.log('✅ Transformed data:', transformedData);

      set({
        teachers: transformedData.teachers,
        classrooms: transformedData.classrooms,
        levels: transformedData.levels,
        timeSlots: transformedData.timeSlots,
        isLoading: false,
      });
      notify.success('Schedule data loaded successfully');
    } catch (error) {
      set({ isLoading: false });
      console.warn('❌ Could not load data from server:', error);

      // If no data exists locally, create some default data
      const { teachers, classrooms, levels } = get();
      if (teachers.length === 0 && classrooms.length === 0 && levels.length === 0) {
        console.log('🔧 Creating default data...');
        set({
          teachers: [
            { id: '1', name: 'John Smith', color: '#3B82F6' },
            { id: '2', name: 'Sarah Johnson', color: '#EF4444' },
            { id: '3', name: 'Mike Davis', color: '#10B981' },
          ],
          classrooms: [
            { id: '1', name: 'Room A', capacity: 30 },
            { id: '2', name: 'Room B', capacity: 25 },
            { id: '3', name: 'Lab 1', capacity: 20 },
          ],
          levels: [
            { id: '1', label: 'Beginner', color: '#10B981' },
            { id: '2', label: 'Intermediate', color: '#F59E0B' },
            { id: '3', label: 'Advanced', color: '#EF4444' },
          ],
        });
        notify.info('Using default data - backend not available');
      }
    }
  },

  saveScheduleData: async () => {
    const { teachers, classrooms, levels, timeSlots } = get();
    await withNotifications(
      async () => {
        await scheduleApi.saveSchedule({
          teachers,
          classrooms,
          levels,
          timeSlots,
        });
      },
      {
        loading: 'Saving schedule...',
        success: 'Schedule saved successfully',
        errorPrefix: 'Failed to save schedule',
      }
    );
  },

  createTeacherAsync: async (teacher) => {
    const result = await withNotifications(
      async () => {
        const newTeacher = await scheduleApi.createTeacher(teacher);
        set((state) => ({ teachers: [...state.teachers, newTeacher] }));
        return newTeacher;
      },
      {
        loading: 'Adding teacher...',
        success: 'Teacher added successfully',
        errorPrefix: 'Failed to add teacher',
      }
    );
  },

  updateTeacherAsync: async (id, updates) => {
    await withNotifications(
      async () => {
        const updatedTeacher = await scheduleApi.updateTeacher(id, updates);
        set((state) => ({
          teachers: state.teachers.map((t) => (t.id === id ? updatedTeacher : t)),
        }));
      },
      {
        loading: 'Updating teacher...',
        success: 'Teacher updated successfully',
        errorPrefix: 'Failed to update teacher',
      }
    );
  },

  deleteTeacherAsync: async (id) => {
    await withNotifications(
      async () => {
        await scheduleApi.deleteTeacher(id);
        set((state) => ({
          teachers: state.teachers.filter((t) => t.id !== id),
          timeSlots: state.timeSlots.filter((ts) => ts.teacherId !== id),
        }));
      },
      {
        loading: 'Removing teacher...',
        success: 'Teacher removed successfully',
        errorPrefix: 'Failed to remove teacher',
      }
    );
  },

  createClassroomAsync: async (classroom) => {
    await withNotifications(
      async () => {
        const newClassroom = await scheduleApi.createClassroom(classroom);
        set((state) => ({ classrooms: [...state.classrooms, newClassroom] }));
      },
      {
        loading: 'Adding classroom...',
        success: 'Classroom added successfully',
        errorPrefix: 'Failed to add classroom',
      }
    );
  },

  updateClassroomAsync: async (id, updates) => {
    await withNotifications(
      async () => {
        const updatedClassroom = await scheduleApi.updateClassroom(id, updates);
        set((state) => ({
          classrooms: state.classrooms.map((c) => (c.id === id ? updatedClassroom : c)),
        }));
      },
      {
        loading: 'Updating classroom...',
        success: 'Classroom updated successfully',
        errorPrefix: 'Failed to update classroom',
      }
    );
  },

  deleteClassroomAsync: async (id) => {
    await withNotifications(
      async () => {
        await scheduleApi.deleteClassroom(id);
        set((state) => ({
          classrooms: state.classrooms.filter((c) => c.id !== id),
          timeSlots: state.timeSlots.filter((ts) => ts.classroomId !== id),
        }));
      },
      {
        loading: 'Removing classroom...',
        success: 'Classroom removed successfully',
        errorPrefix: 'Failed to remove classroom',
      }
    );
  },

  createLevelAsync: async (level) => {
    await withNotifications(
      async () => {
        const newLevel = await scheduleApi.createLevel(level);
        set((state) => ({ levels: [...state.levels, newLevel] }));
      },
      {
        loading: 'Adding level...',
        success: 'Level added successfully',
        errorPrefix: 'Failed to add level',
      }
    );
  },

  updateLevelAsync: async (id, updates) => {
    await withNotifications(
      async () => {
        const updatedLevel = await scheduleApi.updateLevel(id, updates);
        set((state) => ({
          levels: state.levels.map((l) => (l.id === id ? updatedLevel : l)),
        }));
      },
      {
        loading: 'Updating level...',
        success: 'Level updated successfully',
        errorPrefix: 'Failed to update level',
      }
    );
  },

  deleteLevelAsync: async (id) => {
    await withNotifications(
      async () => {
        await scheduleApi.deleteLevel(id);
        set((state) => ({
          levels: state.levels.filter((l) => l.id !== id),
          timeSlots: state.timeSlots.filter((ts) => ts.levelId !== id),
        }));
      },
      {
        loading: 'Removing level...',
        success: 'Level removed successfully',
        errorPrefix: 'Failed to remove level',
      }
    );
  },

  createTimeSlotAsync: async (timeSlot) => {
    console.log('Creating time slot with data:', timeSlot);
    try {
      await withNotifications(
        async () => {
          const newTimeSlot = await scheduleApi.createTimeSlot(timeSlot);
          console.log('Time slot created successfully:', newTimeSlot);
          set((state) => ({ timeSlots: [...state.timeSlots, newTimeSlot] }));
        },
        {
          loading: 'Adding time slot...',
          success: 'Time slot added successfully',
          errorPrefix: 'Failed to add time slot',
        }
      );
    } catch (error) {
      console.error('Error creating time slot:', error);
      // If API fails, try to add locally with a generated ID
      const localTimeSlot = {
        ...timeSlot,
        id: `local-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      };
      set((state) => ({ timeSlots: [...state.timeSlots, localTimeSlot] }));
      notify.info('Time slot added locally', 'Will sync when backend is available');
    }
  },

  updateTimeSlotAsync: async (id, updates) => {
    await withNotifications(
      async () => {
        const updatedTimeSlot = await scheduleApi.updateTimeSlot(id, updates);
        set((state) => ({
          timeSlots: state.timeSlots.map((ts) => (ts.id === id ? updatedTimeSlot : ts)),
        }));
      },
      {
        loading: 'Updating time slot...',
        success: 'Time slot updated successfully',
        errorPrefix: 'Failed to update time slot',
      }
    );
  },

  deleteTimeSlotAsync: async (id) => {
    console.log('Deleting time slot with ID:', id);
    try {
      await withNotifications(
        async () => {
          await scheduleApi.deleteTimeSlot(id);
          set((state) => ({
            timeSlots: state.timeSlots.filter((ts) => ts.id !== id),
          }));
        },
        {
          loading: 'Removing time slot...',
          success: 'Time slot removed successfully',
          errorPrefix: 'Failed to remove time slot',
        }
      );
    } catch (error) {
      console.error('Error deleting time slot from API:', error);
      // If API fails, still delete locally for offline functionality
      set((state) => ({
        timeSlots: state.timeSlots.filter((ts) => ts.id !== id),
      }));
      notify.info('Time slot removed locally', 'Will sync when backend is available');
    }
  },

  getFilteredTimeSlots: () => {
    const { timeSlots, selectedTeacher, selectedClassroom } = get();
    return timeSlots.filter((slot) => {
      if (selectedTeacher && slot.teacherId !== selectedTeacher) return false;
      if (selectedClassroom && slot.classroomId !== selectedClassroom) return false;
      return true;
    });
  },

  // Reset to default data (useful for testing)
  resetToDefaults: () => {
    set({
      teachers: [
        { id: '1', name: 'John Smith', color: '#3B82F6' },
        { id: '2', name: 'Sarah Johnson', color: '#EF4444' },
        { id: '3', name: 'Mike Davis', color: '#10B981' },
      ],
      classrooms: [
        { id: '1', name: 'Room A', capacity: 30 },
        { id: '2', name: 'Room B', capacity: 25 },
        { id: '3', name: 'Lab 1', capacity: 20 },
      ],
      levels: [
        { id: '1', label: 'Beginner', color: '#10B981' },
        { id: '2', label: 'Intermediate', color: '#F59E0B' },
        { id: '3', label: 'Advanced', color: '#EF4444' },
      ],
      timeSlots: [],
      availableTimeSlots: ['08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00'],
    });
    notify.success('Data reset to defaults');
  },

  // Debug function to test API
  testApiConnection: async () => {
    try {
      console.log('🔍 Testing API connection...');
      const response = await fetch('http://localhost:5000/api/schedule/data');
      const text = await response.text();
      console.log('📡 Raw response:', text);

      try {
        const json = JSON.parse(text);
        console.log('📊 Parsed JSON:', json);
        notify.success('API connection successful - check console for details');
      } catch (parseError) {
        console.error('❌ JSON parse error:', parseError);
        notify.error('API returned invalid JSON');
      }
    } catch (error) {
      console.error('❌ API connection failed:', error);
      notify.error('Failed to connect to API');
    }
  },

  checkConflict: (newSlot) => {
    const { timeSlots } = get();
    return timeSlots.some((slot) => {
      // Skip if different days
      if (slot.dayOfWeek !== newSlot.dayOfWeek) return false;

      // Parse times for comparison
      const slotStart = new Date(`2024-01-01 ${slot.start}`);
      const slotEnd = new Date(`2024-01-01 ${slot.end}`);
      const newStart = new Date(`2024-01-01 ${newSlot.start}`);
      const newEnd = new Date(`2024-01-01 ${newSlot.end}`);

      // Check for time overlap
      const timeOverlap = newStart < slotEnd && newEnd > slotStart;

      if (!timeOverlap) return false;

      // Only conflict if same teacher OR same classroom is being used
      // This allows multiple classes at the same time with different teachers/rooms
      const sameTeacher = slot.teacherId === newSlot.teacherId;
      const sameClassroom = slot.classroomId === newSlot.classroomId;

      return sameTeacher || sameClassroom;
    });
  },
    }),
    {
      name: 'schedule-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        teachers: state.teachers,
        classrooms: state.classrooms,
        levels: state.levels,
        timeSlots: state.timeSlots,
        availableTimeSlots: state.availableTimeSlots,
      }),
    }
  )
);
